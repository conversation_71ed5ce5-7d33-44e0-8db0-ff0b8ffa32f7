import asyncio
import itertools
import json

# https://github.com/GateNLP/ultimate-sitemap-parser
from usp.tree import sitemap_tree_for_homepage

# https://github.com/UKPLab/sentence-transformers
from sentence_transformers import SentenceTransformer, util

# https://github.com/trafilatura/trafilatura
import trafilatura

# https://github.com/networkx/networkx
import networkx as nx

# https://github.com/Textualize/rich
from rich.console import Console
from rich.progress import (
    Progress,
    SpinnerColumn,
    TextColumn,
    BarColumn,
    TaskProgressColumn,
)
from rich.panel import Panel

# https://github.com/autoscrape-labs/pydoll
from pydoll.browser import Chrome

console = Console()


async def scraping(urls):
    async with Chrome() as browser:
        site_responses = {}

        tab = await browser.start(headless=True)

        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TaskProgressColumn(),
            console=console,
        ) as progress:
            task = progress.add_task("Extraction en cours...", total=len(urls))

            for url in urls:
                progress.update(task, description=f"Traitement: {url[:50]}...")

                console.print(f"🔍 Extraction du contenu de la page: '{url}'...")

                try:
                    await tab.go_to(url)

                    # Wait for page to load
                    await asyncio.sleep(2)

                    # Get the actual current URL (handles redirects)
                    current_url_result = await tab.execute_script("return window.location.href")
                    current_url = current_url_result.get("result", {}).get("result", {}).get("value", url)

                    title_result = await tab.execute_script("return document.title")
                    title = title_result.get("result", {}).get("result", {}).get("value", "No Title")

                    html_result = await tab.execute_script("return document.documentElement.outerHTML")
                    html_code = html_result.get("result", {}).get("result", {}).get("value", "")

                    links = await tab.find(tag_name="a", find_all=True)
                    link_count = len(links) if isinstance(links, list) else (1 if links else 0)

                    site_responses[url] = {
                        "actual_url": current_url,
                        "title": title,
                        "html_code": html_code,
                        "link_count": link_count,
                        "status_code": 200,  # Assume success if we got here
                    }
                except (RuntimeError, ConnectionError, ValueError) as e:
                    console.print(f"[red]❌ Erreur lors du traitement de {url}: {e}[/red]")
                    site_responses[url] = {
                        "requested_url": url,
                        "actual_url": url,
                        "title": "Error",
                        "html_code": "",
                        "link_count": 0,
                        "status_code": 500,
                        "error": str(e)
                    }

                progress.advance(task)

        return site_responses


async def main():
    console.print(
        Panel.fit("🚀 Analyseur de Similarité HTML Français", style="bold magenta")
    )

    console.print("[cyan]📡 Récupération des URLs depuis le sitemap...[/cyan]")
    site_urls = []

    site_url ="https://zonetuto.fr/"
    #site_url = "http://cocon.se/"
    # site_url = "https://www.patrickcoquart.com/"

    sitemap_tree = sitemap_tree_for_homepage(
        homepage_url=site_url,
        use_known_paths=False,
    )
    if len(list(sitemap_tree.all_pages())) == 0:
        sitemap_tree = sitemap_tree_for_homepage(
            homepage_url=site_url,
            use_known_paths=True,
        )

    for page in sitemap_tree.all_pages():
        site_urls.append(page.url)
    console.print(f"[green]✅ {len(site_urls)} URLs trouvées dans le sitemap[/green]")

    site_responses = await scraping(site_urls)

    site_name = site_url.replace("https://", "").replace("http://", "").replace("/", "").replace(".", "_")
    json_filename = f"site_responses_{site_name}.json"
    try:
        with open(json_filename, 'w', encoding='utf-8') as json_file:
            json.dump(site_responses, json_file, ensure_ascii=False, indent=2)
        console.print(f"[green]💾 Sauvegarde des données:'{json_filename}'[/green]")
    except (IOError, json.JSONDecodeError) as e:
        console.print(f"[red]❌ Erreur lors de la sauvegarde: '{json_filename}' Erreur: {e}[/red]")

    #main_text = trafilatura.extract(response.text)

if __name__ == "__main__":
    asyncio.run(main())
